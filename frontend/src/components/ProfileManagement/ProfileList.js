/**
 * ProfileList Component - Display and manage profiles in a table
 */

import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Popconfirm,
  message,
  Tooltip,
  Typography,
  Card
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  ChromeOutlined,
  SaveOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { apiService } from '../../services/api';
import ProfileDataModal from './ProfileDataModal';

const { Text } = Typography;

const ProfileList = ({
  profiles = [],
  loading = false,
  onEdit,
  onCreate,
  onRefresh,
  onDelete // New prop for custom delete function
}) => {
  const [deleting, setDeleting] = useState(null);
  const [profileDataModalVisible, setProfileDataModalVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);

  const handleDelete = async (profile) => {
    setDeleting(profile.id);
    try {
      console.log('Deleting profile:', profile.id);

      // Use custom delete function if provided, otherwise use default FastAPI endpoint
      const response = onDelete
        ? await onDelete(profile.id)
        : await apiService.deleteProfile(profile.id);

      console.log('Delete response:', response);

      if (response.success || response.message) {
        message.success('Profile deleted successfully!');
        onRefresh();
      } else {
        message.error(response.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete profile error:', error);
      console.error('Error response:', error.response?.data);

      // Check if it's actually a success but wrapped in error
      if (error.response?.status === 200 || error.response?.data?.success) {
        console.log('Success response wrapped in error, processing...');
        message.success('Profile deleted successfully!');
        onRefresh();
      } else {
        const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || 'Delete failed';
        message.error(`Delete failed: ${errorMessage}`);
      }
    } finally {
      setDeleting(null);
    }
  };

  const handleLaunchBrowser = async (profile) => {
    try {
      // First check if we have the account information
      if (!profile.account_id) {
        message.error('No account associated with this profile');
        return;
      }

      // Launch browser using NestJS backend
      const response = await apiService.launchBrowserWithProfile(profile.account_id);

      if (response.data.success) {
        message.success('Browser launched successfully');
      } else {
        message.error(response.data.message || 'Failed to launch browser');
      }
    } catch (error) {
      console.error('Launch browser error:', error);
      message.error(error.response?.data?.message || error.message || 'Failed to launch browser');
    }
  };

  const handleSaveProfile = async (profile) => {
    try {
      if (!profile.account_id) {
        message.error('No account associated with this profile');
        return;
      }

      // Capture real browser data from the active browser session
      message.loading('Đang capture dữ liệu từ browser...', 0);

      try {
        // Use the profile ID to capture browser data
        const captureResponse = await apiService.post(`/api/profiles/${profile.id}/capture-browser-data`);

        message.destroy(); // Clear loading message

        console.log('Capture response:', captureResponse); // Debug log

        if (!captureResponse.success) {
          message.error(`Lỗi capture data: ${captureResponse.message}`);
          return;
        }

        const realBrowserData = captureResponse.data;
        message.loading('Đã capture dữ liệu thành công, đang lưu...', 0);

        console.log('Real browser data:', realBrowserData); // Debug log

        // Convert the captured data to the format expected by saveProfileData
        const profileData = {
          localStorage: {},
          indexedDB: {
            databases: [],
            data: {}
          },
          history: [],
          cookies: realBrowserData.cookies || []
        };

        // Convert localStorage array to object format
        if (realBrowserData.localStorage && Array.isArray(realBrowserData.localStorage)) {
          realBrowserData.localStorage.forEach(item => {
            if (item.key && item.value) {
              profileData.localStorage[item.key] = item.value;
            }
          });
        }

        // Add current page to history if available
        if (realBrowserData.current_page && realBrowserData.current_page.url) {
          profileData.history.push({
            url: realBrowserData.current_page.url,
            title: realBrowserData.current_page.title || 'Untitled',
            visitTime: realBrowserData.captured_at || new Date().toISOString()
          });
        }

        message.destroy(); // Clear loading message

        console.log('Converted profile data:', profileData); // Debug log

        const response = await apiService.saveProfileData(profile.account_id, profileData);

        console.log('Save response:', response); // Debug log

      } catch (captureError) {
        message.destroy(); // Clear loading message
        console.error('Capture error details:', captureError);
        console.error('Error response:', captureError.response?.data);

        // Check if it's actually a success but wrapped in error
        if (captureError.response?.status === 200 || captureError.response?.data?.success) {
          console.log('Success response wrapped in error, continuing...');
          try {
            const captureResponse = captureError.response.data;
            const realBrowserData = captureResponse.data;

            message.loading('Đã capture dữ liệu thành công, đang lưu...', 0);

            // Convert the captured data to the format expected by saveProfileData
            const profileData = {
              localStorage: {},
              indexedDB: {
                databases: [],
                data: {}
              },
              history: [],
              cookies: realBrowserData.cookies || []
            };

            // Convert localStorage array to object format
            if (realBrowserData.localStorage && Array.isArray(realBrowserData.localStorage)) {
              realBrowserData.localStorage.forEach(item => {
                if (item.key && item.value) {
                  profileData.localStorage[item.key] = item.value;
                }
              });
            }

            // Add current page to history if available
            if (realBrowserData.current_page && realBrowserData.current_page.url) {
              profileData.history.push({
                url: realBrowserData.current_page.url,
                title: realBrowserData.current_page.title || 'Untitled',
                visitTime: realBrowserData.captured_at || new Date().toISOString()
              });
            }

            message.destroy(); // Clear loading message

            const response = await apiService.saveProfileData(profile.account_id, profileData);

            if (response.data.success) {
              message.success(`Đã lưu thành công! Captured ${realBrowserData.localStorage?.length || 0} localStorage items, ${realBrowserData.cookies?.length || 0} cookies`);
              if (onRefresh) {
                onRefresh();
              }
              return;
            }
          } catch (retryError) {
            console.error('Retry error:', retryError);
          }
        }

        message.error('Không thể capture dữ liệu từ browser. Có thể browser chưa được launch hoặc đã đóng.');
        return;
      }

      if (response.data.success) {
        message.success('Profile data saved successfully');
        // Refresh the profile list to show updated sync status
        if (onRefresh) {
          onRefresh();
        }
      } else {
        message.error(response.data.message || 'Failed to save profile data');
      }
    } catch (error) {
      console.error('Save profile error:', error);
      message.error(error.response?.data?.message || error.message || 'Failed to save profile data');
    }
  };

  const handleViewProfileData = (profile) => {
    setSelectedProfile(profile);
    setProfileDataModalVisible(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'orange';
      case 'archived': return 'red';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Profile Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          {record.description && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          )}
        </Space>
      ),
    },
    {
      title: 'Profile Group',
      dataIndex: ['profileGroup', 'name'],
      key: 'profileGroup',
      render: (text) => (
        <Tag color="blue">{text}</Tag>
      ),
    },
    {
      title: 'Account',
      dataIndex: ['account', 'browser_profile_id'],
      key: 'account',
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Text>{text || `Account ${record.account?.id}`}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.account?.id}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(date).format('MMM DD, YYYY')}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(date).format('HH:mm')}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Created By',
      dataIndex: ['createdByAdmin', 'email'],
      key: 'createdBy',
      render: (email) => (
        <Text type="secondary">{email}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                // TODO: Implement view details modal
                message.info('View details feature coming soon');
              }}
            />
          </Tooltip>
          
          <Tooltip title="Launch Browser">
            <Button
              type="text"
              icon={<ChromeOutlined />}
              size="small"
              onClick={() => handleLaunchBrowser(record)}
            />
          </Tooltip>

          <Tooltip title="Save Profile Data">
            <Button
              type="text"
              icon={<SaveOutlined />}
              size="small"
              onClick={() => handleSaveProfile(record)}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>

          <Tooltip title="View Profile Data">
            <Button
              type="text"
              icon={<DatabaseOutlined />}
              size="small"
              onClick={() => handleViewProfileData(record)}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>

          <Tooltip title="Edit Profile">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => onEdit(record)}
            />
          </Tooltip>

          <Popconfirm
            title="Delete Profile"
            description="Are you sure you want to delete this profile?"
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete Profile">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
                loading={deleting === record.id}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Profiles"
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={onCreate}
        >
          Create Profile
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={profiles}
        loading={loading}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} profiles`,
        }}
        scroll={{ x: 1200 }}
      />

      <ProfileDataModal
        visible={profileDataModalVisible}
        onClose={() => {
          setProfileDataModalVisible(false);
          setSelectedProfile(null);
        }}
        profile={selectedProfile}
      />
    </Card>
  );
};

export default ProfileList;
